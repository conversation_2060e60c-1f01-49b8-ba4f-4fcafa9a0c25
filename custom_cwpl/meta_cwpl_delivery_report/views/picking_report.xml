<?xml version="1.0" encoding="utf-8"?>
<odoo>

    <template id="custom_report_picking" inherit_id="stock.report_picking">
        <xpath expr="//div[@class='page']/table[1]" position="replace">
            <table class="table table-sm" t-if="o.move_line_ids and o.move_ids_without_package">
                <t t-set="has_barcode" t-value="any(move_line.product_id and move_line.product_id.sudo().barcode or move_line.package_id for move_line in o.move_line_ids)"/>
                <t t-set="has_serial_number" t-value="any(move_line.lot_id or move_line.lot_name for move_line in o.move_line_ids)" groups="stock.group_production_lot"/>
                <thead>
                    <tr>

                        <th name="th_product">
                            <strong>SL.</strong>
                        </th>
                        <th name="th_product">
                            <strong>Product</strong>
                        </th>
                        <th>
                            <strong>Quantity</strong>
                        </th>
                        <th name="th_from" t-if="o.picking_type_id.code != 'incoming'" align="left" groups="stock.group_stock_multi_locations">
                            <strong>From</strong>
                        </th>
                        <th name="th_to" t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                            <strong>To</strong>
                        </th>
                        <th name="th_serial_number" class="text-center" t-if="has_serial_number">
                           <strong>Lot/Serial Number</strong>
                        </th>
                        <th name="th_barcode" class="text-center" t-if="has_barcode">
                            <strong>Product Barcode</strong>
                        </th>
                    </tr>
                </thead>
                <tbody>
                    <!-- In case you come across duplicated lines, ask NIM or LAP -->
                    <t t-set="row_count" t-value="0"/>
                    <t t-foreach="o.move_line_ids_without_package" t-as="ml">
                        <tr>
                            <t t-set="row_count" t-value="row_count + 1"/>
                            <td>                                        
                                <span t-esc="row_count"/>
                            </td>

                            <td>
                                <span t-field="ml.product_id.display_name"/><br/>
                                <span t-field="ml.product_id.description_picking"/>
                            </td>
                            <td>
                                <span t-if="o.state != 'done'" t-field="ml.product_uom_qty"/>
                                <span t-if="o.state == 'done'" t-field="ml.qty_done"/>
                                <span t-field="ml.product_uom_id" groups="uom.group_uom"/>
                            </td>
                            <td t-if="o.picking_type_id.code != 'incoming'" groups="stock.group_stock_multi_locations">
                                <span t-esc="ml.location_id.display_name"/>
                                    <t t-if="ml.package_id">
                                        <span t-field="ml.package_id"/>
                                    </t>
                            </td>
                            <td t-if="o.picking_type_id.code != 'outgoing'" groups="stock.group_stock_multi_locations">
                                <div>
                                    <span t-field="ml.location_dest_id"/>
                                    <t t-if="ml.result_package_id">
                                        <span t-field="ml.result_package_id"/>
                                    </t>
                                </div>
                            </td>
                            <td class=" text-center h6" t-if="has_serial_number">
                                <div t-if="has_serial_number and (ml.lot_id or ml.lot_name)" t-esc="ml.lot_id.name or ml.lot_name" t-options="{'widget': 'barcode', 'humanreadable': 1, 'width': 400, 'height': 100, 'img_style': 'width:100%;height:35px;'}"/>
                            </td>
                            <td class="text-center" t-if="has_barcode">
                                <t t-if="product_barcode != ml.product_id.barcode">
                                    <span t-if="ml.product_id and ml.product_id.barcode">
                                        <div t-field="ml.product_id.barcode" t-options="{'widget': 'barcode', 'symbology': 'auto', 'width': 400, 'height': 100, 'quiet': 0, 'img_style': 'height:35px;'}"/>
                                    </span>
                                    <t t-set="product_barcode" t-value="ml.product_id.barcode"/>
                                </t>
                            </td>
                        </tr>
                    </t>
                  </tbody>
            </table>
        </xpath>
    </template>

</odoo>
